# PlugMate Figma Plugin

一个使用 Vite + React 19 + TypeScript + TailwindCSS 构建的现代化多页面 Figma 插件。

## 🚀 特性

- ✅ **React 19** - 使用最新的 React 版本
- ✅ **TypeScript** - 完整的类型安全
- ✅ **TailwindCSS** - 现代化的样式系统
- ✅ **Vite** - 快速的构建工具
- ✅ **多页面路由** - 使用 React Router (HashRouter)
- ✅ **状态管理** - 使用 Zustand
- ✅ **错误边界** - 优雅的错误处理
- ✅ **消息系统** - UI 和插件代码之间的类型安全通信
- ✅ **自动路径修复** - 构建时自动修复 Figma 插件路径问题

## 项目结构

```
├── src/
│   ├── code/               # Figma 插件主代码
│   │   └── index.ts       # 插件入口
│   ├── ui/                # React UI 代码
│   │   ├── components/    # 组件
│   │   ├── pages/         # 页面
│   │   ├── store/         # 状态管理
│   │   ├── styles/        # 样式文件
│   │   ├── App.tsx        # 应用根组件
│   │   └── main.tsx       # UI 入口
│   ├── types/             # 类型定义
│   └── utils/             # 工具函数
├── manifest.json          # Figma 插件配置
├── index.html             # HTML 入口
├── vite.config.ts         # Vite 配置
└── package.json           # 项目配置
```

## 开发环境设置

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

```bash
pnpm dev
```

### 3. 构建插件

```bash
pnpm build
```

构建脚本会自动：
- 运行 Vite 构建
- 修复 HTML 中的路径为相对路径
- 移除不必要的资源引用

### 4. 在 Figma 中开发

1. 打开 Figma Desktop 应用
2. 进入 `Plugins` > `Development` > `Import plugin from manifest...`
3. 选择项目根目录下的 `manifest.json` 文件
4. 插件将出现在开发插件列表中

## 页面结构

插件包含以下页面：

- **首页** (`/`) - 插件概览和快速操作
- **创建** (`/create`) - 创建各种设计元素
- **选择** (`/selection`) - 管理选中的元素
- **设置** (`/settings`) - 插件配置

## 开发指南

### 添加新页面

1. 在 `src/ui/pages/` 创建新的页面组件
2. 在 `src/ui/App.tsx` 中添加路由
3. 在 `src/ui/components/Layout.tsx` 中添加导航项

### 与 Figma API 交互

- UI 向插件代码发送消息：使用 `sendMessage()` 函数
- 插件代码向 UI 发送消息：使用 `sendToUI()` 函数
- 消息格式定义在 `src/types/figma.ts` 中

### 状态管理

项目使用 Zustand 进行状态管理：

- `usePluginStore` - 主要插件状态
- `useMessageStore` - 消息通知状态

## 构建和部署

### 构建生产版本

```bash
npm run build
```

构建后的文件将输出到 `dist/` 目录，包含：

- `code.js` - Figma 插件代码
- `index.html` - UI 界面
- 其他静态资源

### 发布插件

1. 确保 `manifest.json` 中的信息正确
2. 运行构建命令
3. 在 Figma 中发布插件

## 技术栈详情

- **Vite** - 构建工具，支持热重载和快速构建
- **React 18** - UI 框架，支持 Hooks 和现代特性
- **TailwindCSS** - CSS 框架，自定义 Figma 主题色彩
- **TypeScript** - 类型安全，提供良好的开发体验
- **React Router** - 客户端路由
- **Zustand** - 轻量级状态管理
- **PostCSS** - CSS 处理器

## 许可证

MIT License 