import { ComponentType } from 'react';

// Figma Plugin API 类型定义
export interface PluginMessage {
  type: string;
  payload?: unknown;
}

export interface UIMessage extends PluginMessage {
  type: 'ui-message';
  payload: {
    action: UserAction;
    data?: unknown;
  };
}

export interface CodeMessage extends PluginMessage {
  type: 'code-message';
  payload: {
    action: CodeAction;
    data?: unknown;
  };
}

// 页面路由类型
export interface PageRoute {
  path: string;
  name: string;
  component: ComponentType;
  icon?: ComponentType;
}

// Figma 节点信息
export interface FigmaNodeInfo {
  id: string;
  name: string;
  type: string;
  width?: number;
  height?: number;
}

// Figma 用户信息
export interface FigmaUser {
  id: string;
  name: string;
  email?: string;
  photoUrl?: string;
}

// 颜色类型
export interface RGBColor {
  r: number;
  g: number;
  b: number;
}

// 创建形状的选项
export interface ShapeOptions {
  width?: number;
  height?: number;
  name?: string;
  color?: RGBColor;
}

// 创建文本的选项
export interface TextOptions {
  text?: string;
  fontSize?: number;
  name?: string;
  color?: RGBColor;
}

// 插件状态类型
export interface PluginState {
  currentPage: string;
  isLoading: boolean;
  error: string | null;
  selectedNodes: FigmaNodeInfo[];
  user: FigmaUser | null;
}

// 用户操作类型
export type UserAction =
  | 'create-rectangle'
  | 'create-ellipse'
  | 'create-text'
  | 'get-selection'
  | 'close-plugin';

// 代码响应操作类型
export type CodeAction =
  | 'plugin-ready'
  | 'selection-changed'
  | 'selection-updated'
  | 'node-created'
  | 'error';

// 响应数据类型
export interface ActionResponse {
  success: boolean;
  message?: string;
  data?: unknown;
}

// 消息类型
export interface Message {
  id: string;
  type: 'success' | 'error' | 'info';
  content: string;
  timestamp: number;
}