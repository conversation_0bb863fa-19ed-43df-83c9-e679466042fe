// Figma Plugin API 类型定义
export interface PluginMessage {
  type: string;
  payload?: any;
}

export interface UIMessage extends PluginMessage {
  type: 'ui-message';
  payload: {
    action: string;
    data?: any;
  };
}

export interface CodeMessage extends PluginMessage {
  type: 'code-message';
  payload: {
    action: string;
    data?: any;
  };
}

// 页面路由类型
export interface PageRoute {
  path: string;
  name: string;
  component: any; // React.ComponentType
  icon?: any; // React.ComponentType
}

// 插件状态类型
export interface PluginState {
  currentPage: string;
  isLoading: boolean;
  error: string | null;
  selectedNodes: any[];
  user: any | null;
}

// 用户操作类型
export type UserAction = 
  | 'create-rectangle'
  | 'create-ellipse'
  | 'create-text'
  | 'get-selection'
  | 'close-plugin';

// 响应数据类型
export interface ActionResponse {
  success: boolean;
  message?: string;
  data?: any;
} 