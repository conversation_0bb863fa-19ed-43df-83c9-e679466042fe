import type { UIMessage, CodeMessage, UserAction, ActionResponse } from '@types/figma';

// UI 向 Code 发送消息
export function sendToCode(action: UserAction, data?: any): void {
  const message: UIMessage = {
    type: 'ui-message',
    payload: { action, data }
  };
  parent.postMessage({ pluginMessage: message }, '*');
}

// Code 向 UI 发送消息
export function sendToUI(action: string, data?: any): void {
  const message: CodeMessage = {
    type: 'code-message',
    payload: { action, data }
  };
  figma.ui.postMessage(message);
}

// 监听来自 Code 的消息
export function onMessage(callback: (message: CodeMessage) => void): void {
  window.addEventListener('message', (event) => {
    const message = event.data.pluginMessage as CodeMessage;
    if (message && message.type === 'code-message') {
      callback(message);
    }
  });
}

// 创建响应对象
export function createResponse(success: boolean, message?: string, data?: any): ActionResponse {
  return { success, message, data };
}

// 错误处理工具
export function handleError(error: any): ActionResponse {
  console.error('Plugin Error:', error);
  return createResponse(false, error.message || 'An unknown error occurred');
}

// 消息类型常量
export const MESSAGE_TYPES = {
  UI_READY: 'ui-ready',
  SELECTION_CHANGED: 'selection-changed',
  NODE_CREATED: 'node-created',
  ERROR: 'error',
  SUCCESS: 'success',
} as const; 