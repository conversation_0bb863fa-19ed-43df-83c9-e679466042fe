// Figma 插件主代码
// 这个文件在 Figma 的沙盒环境中运行，可以访问 Figma API

console.log('PlugMate Figma Plugin Started');

// 显示 UI
figma.showUI(__html__, {
  width: 400,
  height: 600,
  title: 'PlugMate Plugin'
});

// 监听来自 UI 的消息
figma.ui.onmessage = (msg) => {
  console.log('Message from UI:', msg);
  
  const { type, payload } = msg;
  
  if (type === 'ui-message') {
    const { action, data } = payload;
    
    switch (action) {
      case 'create-rectangle':
        createRectangle(data);
        break;
        
      case 'create-ellipse':
        createEllipse(data);
        break;
        
      case 'create-text':
        createText(data);
        break;
        
      case 'get-selection':
        getSelection();
        break;
        
      case 'close-plugin':
        figma.closePlugin();
        break;
        
      default:
        console.warn('Unknown action:', action);
    }
  }
};

// 监听选择变化
figma.on('selectionchange', () => {
  const selection = figma.currentPage.selection;
  sendToUI('selection-changed', {
    nodes: selection.map(node => ({
      id: node.id,
      name: node.name,
      type: node.type
    }))
  });
});

// 创建矩形
function createRectangle(options: any = {}) {
  const rect = figma.createRectangle();
  rect.resize(options.width || 100, options.height || 100);
  rect.fills = [{
    type: 'SOLID',
    color: options.color || { r: 0.2, g: 0.6, b: 1 }
  }];
  rect.name = options.name || 'Rectangle';
  
  figma.currentPage.appendChild(rect);
  figma.currentPage.selection = [rect];
  figma.viewport.scrollAndZoomIntoView([rect]);
  
  sendToUI('node-created', { type: 'rectangle', id: rect.id });
}

// 创建椭圆
function createEllipse(options: any = {}) {
  const ellipse = figma.createEllipse();
  ellipse.resize(options.width || 100, options.height || 100);
  ellipse.fills = [{
    type: 'SOLID',
    color: options.color || { r: 1, g: 0.4, b: 0.4 }
  }];
  ellipse.name = options.name || 'Ellipse';
  
  figma.currentPage.appendChild(ellipse);
  figma.currentPage.selection = [ellipse];
  figma.viewport.scrollAndZoomIntoView([ellipse]);
  
  sendToUI('node-created', { type: 'ellipse', id: ellipse.id });
}

// 创建文本
function createText(options: any = {}) {
  const text = figma.createText();
  
  // 加载字体
  figma.loadFont({ family: "Roboto", style: "Regular" }).then(() => {
    text.characters = options.text || 'Hello World';
    text.fontSize = options.fontSize || 24;
    text.name = options.name || 'Text';
    
    if (options.color) {
      text.fills = [{
        type: 'SOLID',
        color: options.color
      }];
    }
    
    figma.currentPage.appendChild(text);
    figma.currentPage.selection = [text];
    figma.viewport.scrollAndZoomIntoView([text]);
    
    sendToUI('node-created', { type: 'text', id: text.id });
  }).catch(error => {
    console.error('Failed to load font:', error);
    sendToUI('error', { message: 'Failed to create text: Font loading failed' });
  });
}

// 获取当前选择
function getSelection() {
  const selection = figma.currentPage.selection;
  sendToUI('selection-updated', {
    nodes: selection.map(node => ({
      id: node.id,
      name: node.name,
      type: node.type,
      width: 'width' in node ? node.width : null,
      height: 'height' in node ? node.height : null
    }))
  });
}

// 向 UI 发送消息
function sendToUI(action: string, data?: any) {
  figma.ui.postMessage({
    type: 'code-message',
    payload: { action, data }
  });
}

// 插件初始化
sendToUI('plugin-ready', { 
  user: figma.currentUser,
  page: figma.currentPage.name 
}); 