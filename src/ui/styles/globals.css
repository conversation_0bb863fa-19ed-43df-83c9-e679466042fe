@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', system-ui, sans-serif;
    background-color: #2c2c2c;
    color: #ffffff;
  }
  
  #root {
    height: 100vh;
    overflow: hidden;
  }
}

@layer components {
  .btn-primary {
    @apply bg-figma-accent hover:bg-figma-accent-hover text-white font-medium px-4 py-2 rounded-md transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-figma-bg-secondary hover:bg-gray-600 text-figma-text border border-figma-border font-medium px-4 py-2 rounded-md transition-colors duration-200;
  }
  
  .input-field {
    @apply bg-figma-bg-secondary border border-figma-border text-figma-text px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-figma-accent;
  }
  
  .card {
    @apply bg-figma-bg-secondary border border-figma-border rounded-lg p-4;
  }
  
  .nav-item {
    @apply flex items-center gap-2 px-3 py-2 rounded-md text-figma-text-secondary hover:text-figma-text hover:bg-figma-bg-secondary transition-colors duration-200;
  }
  
  .nav-item.active {
    @apply text-figma-text bg-figma-bg-secondary border-l-2 border-figma-accent;
  }
} 