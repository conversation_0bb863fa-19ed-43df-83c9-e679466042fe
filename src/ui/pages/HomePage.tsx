import React from 'react'
import { usePluginStore } from '../store/pluginStore'
import { usePluginMessage } from '../hooks/usePluginMessage'

const HomePage: React.FC = () => {
  const { user, selectedNodes } = usePluginStore()
  const { sendMessage } = usePluginMessage()
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-figma-text mb-2">
          欢迎使用 PlugMate
        </h1>
        <p className="text-figma-text-secondary">
          强大的多页面 Figma 插件，助力您的设计工作流程
        </p>
      </div>
      
      {/* 用户信息卡片 */}
      {user && (
        <div className="card">
          <h2 className="text-lg font-semibold mb-3">用户信息</h2>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-figma-accent rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
              </span>
            </div>
            <div>
              <p className="font-medium">{user.name || 'Unknown User'}</p>
              <p className="text-sm text-figma-text-secondary">{user.email || 'No email'}</p>
            </div>
          </div>
        </div>
      )}
      
      {/* 选择信息 */}
      <div className="card">
        <h2 className="text-lg font-semibold mb-3">当前选择</h2>
        {selectedNodes.length > 0 ? (
          <div className="space-y-2">
            {selectedNodes.map((node, index) => (
              <div key={index} className="flex justify-between items-center p-2 bg-figma-bg rounded">
                <span>{node.name}</span>
                <span className="text-sm text-figma-text-secondary">{node.type}</span>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-figma-text-secondary">未选择任何对象</p>
        )}
        <button
          onClick={() => sendMessage('get-selection')}
          className="btn-secondary mt-3"
        >
          刷新选择
        </button>
      </div>
      
      {/* 快速操作 */}
      <div className="card">
        <h2 className="text-lg font-semibold mb-3">快速操作</h2>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => sendMessage('create-rectangle')}
            className="btn-primary"
          >
            创建矩形
          </button>
          <button
            onClick={() => sendMessage('create-ellipse')}
            className="btn-primary"
          >
            创建椭圆
          </button>
          <button
            onClick={() => sendMessage('create-text', { text: 'Hello World' })}
            className="btn-primary"
          >
            创建文本
          </button>
          <button
            onClick={() => sendMessage('close-plugin')}
            className="btn-secondary"
          >
            关闭插件
          </button>
        </div>
      </div>
    </div>
  )
}

export default HomePage 