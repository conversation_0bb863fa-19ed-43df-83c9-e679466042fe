import React from 'react'
import { usePluginStore } from '../store/pluginStore'

const SelectionPage: React.FC = () => {
  const { selectedNodes } = usePluginStore()

  const sendMessage = (action: string, data?: any) => {
    parent.postMessage({
      pluginMessage: {
        type: 'ui-message',
        payload: { action, data }
      }
    }, '*')
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-figma-text mb-2">选择管理</h1>
        <p className="text-figma-text-secondary">管理和操作当前选择的元素</p>
      </div>

      <div className="card">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">当前选择</h2>
          <button
            onClick={() => sendMessage('get-selection')}
            className="btn-secondary"
          >
            刷新
          </button>
        </div>

        {selectedNodes.length > 0 ? (
          <div className="space-y-3">
            {selectedNodes.map((node, index) => (
              <div key={index} className="card p-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{node.name}</h3>
                    <p className="text-sm text-figma-text-secondary">{node.type}</p>
                    {node.width && node.height && (
                      <p className="text-xs text-figma-text-secondary mt-1">
                        {node.width} × {node.height}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <button className="text-xs btn-secondary">
                      复制
                    </button>
                    <button className="text-xs btn-secondary">
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-figma-text-secondary">未选择任何元素</p>
            <button
              onClick={() => sendMessage('get-selection')}
              className="btn-primary mt-3"
            >
              获取选择
            </button>
          </div>
        )}
      </div>

      <div className="card">
        <h2 className="text-lg font-semibold mb-4">批量操作</h2>
        <div className="grid grid-cols-2 gap-3">
          <button
            disabled={selectedNodes.length === 0}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            对齐左侧
          </button>
          <button
            disabled={selectedNodes.length === 0}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            对齐右侧
          </button>
          <button
            disabled={selectedNodes.length === 0}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            水平居中
          </button>
          <button
            disabled={selectedNodes.length === 0}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            垂直居中
          </button>
        </div>
      </div>
    </div>
  )
}

export default SelectionPage 