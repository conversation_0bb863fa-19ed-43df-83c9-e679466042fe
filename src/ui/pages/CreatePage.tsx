import React, { useState } from 'react'
import { usePluginMessage } from '../hooks/usePluginMessage'
import type { RGBColor } from '@types/figma'

interface ShapeFormOptions {
  width: number
  height: number
  name: string
  color: string
}

interface TextFormOptions {
  text: string
  fontSize: number
  name: string
  color: string
}

const CreatePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'shapes' | 'text'>('shapes')
  const [shapeOptions, setShapeOptions] = useState<ShapeFormOptions>({
    width: 100,
    height: 100,
    name: '',
    color: '#18a0fb'
  })
  const [textOptions, setTextOptions] = useState<TextFormOptions>({
    text: 'Hello World',
    fontSize: 24,
    name: '',
    color: '#000000'
  })

  const { sendMessage } = usePluginMessage()

  const hexToRgb = (hex: string): RGBColor => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255
    } : { r: 0, g: 0, b: 0 }
  }

  const createShape = (type: 'rectangle' | 'ellipse') => {
    const data = {
      ...shapeOptions,
      color: hexToRgb(shapeOptions.color)
    }
    sendMessage(`create-${type}`, data)
  }

  const createText = () => {
    const data = {
      ...textOptions,
      color: hexToRgb(textOptions.color)
    }
    sendMessage('create-text', data)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-figma-text mb-2">创建元素</h1>
        <p className="text-figma-text-secondary">创建各种设计元素</p>
      </div>

      {/* 标签页 */}
      <div className="border-b border-figma-border">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('shapes')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'shapes'
                ? 'border-figma-accent text-figma-accent'
                : 'border-transparent text-figma-text-secondary hover:text-figma-text'
            }`}
          >
            形状
          </button>
          <button
            onClick={() => setActiveTab('text')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'text'
                ? 'border-figma-accent text-figma-accent'
                : 'border-transparent text-figma-text-secondary hover:text-figma-text'
            }`}
          >
            文本
          </button>
        </nav>
      </div>

      {/* 形状创建 */}
      {activeTab === 'shapes' && (
        <div className="space-y-6">
          <div className="card">
            <h2 className="text-lg font-semibold mb-4">形状属性</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">名称</label>
                <input
                  type="text"
                  value={shapeOptions.name}
                  onChange={(e) => setShapeOptions({...shapeOptions, name: e.target.value})}
                  placeholder="形状名称"
                  className="input-field w-full"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">宽度</label>
                  <input
                    type="number"
                    value={shapeOptions.width}
                    onChange={(e) => setShapeOptions({...shapeOptions, width: Number(e.target.value)})}
                    className="input-field w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">高度</label>
                  <input
                    type="number"
                    value={shapeOptions.height}
                    onChange={(e) => setShapeOptions({...shapeOptions, height: Number(e.target.value)})}
                    className="input-field w-full"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">颜色</label>
                <input
                  type="color"
                  value={shapeOptions.color}
                  onChange={(e) => setShapeOptions({...shapeOptions, color: e.target.value})}
                  className="input-field h-10"
                />
              </div>
            </div>
          </div>

          <div className="card">
            <h2 className="text-lg font-semibold mb-4">创建形状</h2>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => createShape('rectangle')}
                className="btn-primary"
              >
                创建矩形
              </button>
              <button
                onClick={() => createShape('ellipse')}
                className="btn-primary"
              >
                创建椭圆
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 文本创建 */}
      {activeTab === 'text' && (
        <div className="space-y-6">
          <div className="card">
            <h2 className="text-lg font-semibold mb-4">文本属性</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">文本内容</label>
                <textarea
                  value={textOptions.text}
                  onChange={(e) => setTextOptions({...textOptions, text: e.target.value})}
                  className="input-field w-full h-20"
                  placeholder="输入文本内容"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">名称</label>
                <input
                  type="text"
                  value={textOptions.name}
                  onChange={(e) => setTextOptions({...textOptions, name: e.target.value})}
                  placeholder="文本名称"
                  className="input-field w-full"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">字体大小</label>
                  <input
                    type="number"
                    value={textOptions.fontSize}
                    onChange={(e) => setTextOptions({...textOptions, fontSize: Number(e.target.value)})}
                    className="input-field w-full"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">颜色</label>
                  <input
                    type="color"
                    value={textOptions.color}
                    onChange={(e) => setTextOptions({...textOptions, color: e.target.value})}
                    className="input-field h-10"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <button
              onClick={createText}
              className="btn-primary w-full"
            >
              创建文本
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default CreatePage 