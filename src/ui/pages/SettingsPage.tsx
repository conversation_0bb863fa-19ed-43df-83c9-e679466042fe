import React from 'react'

const SettingsPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-figma-text mb-2">设置</h1>
        <p className="text-figma-text-secondary">插件配置和首选项</p>
      </div>

      <div className="card">
        <h2 className="text-lg font-semibold mb-4">插件信息</h2>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>插件名称</span>
            <span className="text-figma-text-secondary">PlugMate</span>
          </div>
          <div className="flex justify-between">
            <span>版本</span>
            <span className="text-figma-text-secondary">1.0.0</span>
          </div>
          <div className="flex justify-between">
            <span>开发者</span>
            <span className="text-figma-text-secondary">Your Name</span>
          </div>
        </div>
      </div>

      <div className="card">
        <h2 className="text-lg font-semibold mb-4">操作</h2>
        <div className="space-y-3">
          <button className="btn-secondary w-full text-left">
            重置插件设置
          </button>
          <button className="btn-secondary w-full text-left">
            清除缓存
          </button>
          <button className="btn-secondary w-full text-left">
            导出配置
          </button>
          <button className="btn-secondary w-full text-left">
            导入配置
          </button>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage 