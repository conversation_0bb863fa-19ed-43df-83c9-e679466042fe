import React, { useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { usePluginStore, useMessageStore } from './store/pluginStore'
import Layout from './components/Layout'
import HomePage from './pages/HomePage'
import CreatePage from './pages/CreatePage'
import SelectionPage from './pages/SelectionPage'
import SettingsPage from './pages/SettingsPage'
import MessageToast from './components/MessageToast'

function App() {
  const { setUser, setError } = usePluginStore()
  const { addMessage } = useMessageStore()
  
  useEffect(() => {
    // 监听来自插件代码的消息
    const handleMessage = (event: MessageEvent) => {
      const message = event.data.pluginMessage
      
      if (message && message.type === 'code-message') {
        const { action, data } = message.payload
        
        switch (action) {
          case 'plugin-ready':
            setUser(data.user)
            addMessage('success', `插件已启动 - ${data.page}`)
            break
            
          case 'selection-changed':
            // 这里可以更新选择状态
            console.log('Selection changed:', data)
            break
            
          case 'node-created':
            addMessage('success', `已创建 ${data.type}`)
            break
            
          case 'error':
            setError(data.message)
            addMessage('error', data.message)
            break
            
          default:
            console.log('Unhandled message:', action, data)
        }
      }
    }
    
    window.addEventListener('message', handleMessage)
    
    // 告知插件代码 UI 已准备就绪
    parent.postMessage({ pluginMessage: { type: 'ui-ready' } }, '*')
    
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [setUser, setError, addMessage])
  
  return (
    <div className="app">
      <Layout>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/create" element={<CreatePage />} />
          <Route path="/selection" element={<SelectionPage />} />
          <Route path="/settings" element={<SettingsPage />} />
        </Routes>
      </Layout>
      <MessageToast />
    </div>
  )
}

export default App 