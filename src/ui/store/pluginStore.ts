import { create } from 'zustand'
import type { FigmaUser, FigmaNodeInfo, Message } from '@types/figma'

interface PluginState {
  // 基础状态
  isLoading: boolean
  error: string | null
  currentPage: string

  // 用户信息
  user: FigmaUser | null

  // 选中的节点
  selectedNodes: FigmaNodeInfo[]

  // 页面历史
  pageHistory: string[]

  // Actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setCurrentPage: (page: string) => void
  setUser: (user: FigmaUser | null) => void
  setSelectedNodes: (nodes: FigmaNodeInfo[]) => void
  addToHistory: (page: string) => void
  clearError: () => void
}

export const usePluginStore = create<PluginState>((set, get) => ({
  // Initial state
  isLoading: false,
  error: null,
  currentPage: 'home',
  user: null,
  selectedNodes: [],
  pageHistory: ['home'],
  
  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  
  setError: (error) => set({ error }),
  
  setCurrentPage: (page) => {
    set({ currentPage: page })
    get().addToHistory(page)
  },
  
  setUser: (user) => set({ user }),
  
  setSelectedNodes: (nodes) => set({ selectedNodes: nodes }),
  
  addToHistory: (page) => {
    const { pageHistory } = get()
    if (pageHistory[pageHistory.length - 1] !== page) {
      set({ pageHistory: [...pageHistory, page] })
    }
  },
  
  clearError: () => set({ error: null }),
}))

// 消息处理相关的状态
interface MessageState {
  messages: Message[]

  addMessage: (type: 'success' | 'error' | 'info', content: string) => void
  removeMessage: (id: string) => void
  clearMessages: () => void
}

export const useMessageStore = create<MessageState>((set) => ({
  messages: [],
  
  addMessage: (type, content) => {
    const id = Date.now().toString()
    const message = {
      id,
      type,
      content,
      timestamp: Date.now()
    }
    
    set((state) => ({
      messages: [...state.messages, message]
    }))
    
    // 自动移除消息（3秒后）
    setTimeout(() => {
      set((state) => ({
        messages: state.messages.filter(m => m.id !== id)
      }))
    }, 3000)
  },
  
  removeMessage: (id) => {
    set((state) => ({
      messages: state.messages.filter(m => m.id !== id)
    }))
  },
  
  clearMessages: () => set({ messages: [] }),
})) 