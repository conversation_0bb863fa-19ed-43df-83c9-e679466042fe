import React from 'react'
import { useMessageStore } from '../store/pluginStore'

const MessageToast: React.FC = () => {
  const { messages, removeMessage } = useMessageStore()

  if (messages.length === 0) return null

  return (
    <div className="fixed top-4 right-4 space-y-2 z-50">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`px-4 py-3 rounded-md shadow-lg border min-w-[300px] max-w-[400px] ${
            message.type === 'success' 
              ? 'bg-green-600 border-green-500 text-white'
              : message.type === 'error'
              ? 'bg-red-600 border-red-500 text-white'
              : 'bg-figma-bg-secondary border-figma-border text-figma-text'
          }`}
        >
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <p className="text-sm">{message.content}</p>
            </div>
            <button
              onClick={() => removeMessage(message.id)}
              className="ml-3 text-sm opacity-70 hover:opacity-100"
            >
              ✕
            </button>
          </div>
        </div>
      ))}
    </div>
  )
}

export default MessageToast 