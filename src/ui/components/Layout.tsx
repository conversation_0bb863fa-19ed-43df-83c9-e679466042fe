import React from 'react'
import { useLocation, Link } from 'react-router-dom'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation()
  
  const navigationItems = [
    { path: '/', name: '首页', icon: '🏠' },
    { path: '/create', name: '创建', icon: '➕' },
    { path: '/selection', name: '选择', icon: '🎯' },
    { path: '/settings', name: '设置', icon: '⚙️' },
  ]
  
  return (
    <div className="flex h-screen bg-figma-bg">
      {/* 侧边导航 */}
      <div className="w-64 bg-figma-bg-secondary border-r border-figma-border flex flex-col">
        {/* Logo */}
        <div className="p-4 border-b border-figma-border">
          <h1 className="text-lg font-bold text-figma-text">PlugMate</h1>
          <p className="text-sm text-figma-text-secondary">Figma Plugin</p>
        </div>
        
        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`nav-item ${
                    location.pathname === item.path ? 'active' : ''
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        
        {/* 底部信息 */}
        <div className="p-4 border-t border-figma-border">
          <p className="text-xs text-figma-text-secondary">
            Version 1.0.0
          </p>
        </div>
      </div>
      
      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default Layout 