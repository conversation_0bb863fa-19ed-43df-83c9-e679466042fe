import { useCallback } from 'react'
import type { UserAction } from '@types/figma'

/**
 * Hook for sending messages to the Figma plugin code
 */
export const usePluginMessage = () => {
  const sendMessage = useCallback((action: UserAction, data?: unknown) => {
    parent.postMessage({
      pluginMessage: {
        type: 'ui-message',
        payload: { action, data }
      }
    }, '*')
  }, [])

  return { sendMessage }
}
