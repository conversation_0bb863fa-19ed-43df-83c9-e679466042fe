import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      input: {
        // UI 页面入口
        ui: resolve(__dirname, 'index.html'),
        // Figma 插件代码入口
        code: resolve(__dirname, 'src/code/index.ts'),
      },
      output: {
        // 为不同入口生成不同的输出文件
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'code') {
            return 'code.js'
          }
          return '[name].[hash].js'
        },
        chunkFileNames: '[name].[hash].js',
        assetFileNames: '[name].[hash].[ext]'
      }
    },
    outDir: 'dist',
    emptyOutDir: true,
  },
  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
  },
  // 路径别名
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@ui': resolve(__dirname, 'src/ui'),
      '@code': resolve(__dirname, 'src/code'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
    }
  }
}) 