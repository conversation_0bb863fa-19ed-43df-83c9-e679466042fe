#!/usr/bin/env node

import { execSync } from 'child_process'
import { readFileSync, writeFileSync } from 'fs'
import { resolve } from 'path'

console.log('🔨 Building Figma plugin...')

// 运行 Vite 构建
execSync('vite build', { stdio: 'inherit' })

// 修复 HTML 文件中的路径
const htmlPath = resolve('dist/index.html')
let htmlContent = readFileSync(htmlPath, 'utf-8')

// 将绝对路径替换为相对路径
htmlContent = htmlContent.replace(/src="\/([^"]+)"/g, 'src="./$1"')
htmlContent = htmlContent.replace(/href="\/([^"]+)"/g, 'href="./$1"')

// 移除 vite.svg 引用（Figma 插件不需要）
htmlContent = htmlContent.replace(/<link rel="icon"[^>]*>/g, '')

writeFileSync(htmlPath, htmlContent)

console.log('✅ Build completed! HTML paths fixed for Figma plugin.')
console.log('📁 Files generated in dist/ directory')
